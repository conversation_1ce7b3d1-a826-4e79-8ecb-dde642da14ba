<template>
  <div
    v-if="cateInfo?.goodsList?.length"
    :data-spm-box="cateInfo?.spmCode"
    :data-spm-param="cateInfo?.tagId || cateInfo?.selectorId || null"
  >
    <a :href="linkUrl">
      <img
        loading="lazy"
        class="mb-[0.24rem]"
        v-if="cateInfo?.bannerUrl"
        :src="cateInfo?.bannerUrl"
        :alt="
          cateInfo?.tagName ||
          cateInfo?.categoryName ||
          cateInfo?.selectorName ||
          cateInfo?.title
        "
        referrerpolicy="no-referrer"
      />
    </a>

    <div
      class="category-wrapper"
      :style="{ backgroundColor: props.cateColor.cateColor }"
      v-if="!cateInfo?.bannerUrl"
    >
      <a class="category-content" :href="linkUrl"
        ><div
          class="category-title"
          :style="{ color: props.cateColor.textColor }"
        >
          {{
            cateInfo?.tagName ||
            cateInfo?.categoryName ||
            cateInfo?.selectorName ||
            cateInfo?.title
          }}
        </div>
        <icon-card
          size="20"
          name="iconoir:arrow-right"
          :color="props.cateColor.textColor"
        />
      </a>
    </div>
    <div class="goods-wrapper">
      <div
        class="goods-item"
        v-for="goods in cateInfo?.goodsList"
        :key="goods?.goodsId"
      >
        <a
          v-bind:href="`/h5/goods/${goods.goodsId}${
            goods.padc ? `?padc=${goods.padc}` : ''
          }`"
        >
          <n-flex vertical :style="{ gap: '0' }">
            <img
              loading="lazy"
              class="goods-img"
              :alt="goods.goodsName"
              :src="goods.mainImageUrl"
              referrerpolicy="no-referrer"
            />
            <p class="goods-title">
              {{ goods.goodsName }}
            </p>
            <p class="goods-price">
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <!-- <span v-if="goods.minPrice != goods.maxPrice"
                >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
              >
              <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
              <span>{{ setUnit(goods.minPrice) }}</span>
            </p>
            <div
              v-if="goods.pcsEstimateFreight"
              class="text-[0.24rem] mb-[0.12rem] mt-[0.1rem] whitespace-normal"
            >
              <div v-if="goods?.supportOnlineOrder">
                {{ authStore.i18n("cm_goods.finalShippingCost") }}:
              </div>
              <div v-else>{{ authStore.i18n("cm_goods.shippingCost") }}:</div>
              <span class="text-[0.24rem]">{{
                setUnit(goods.pcsEstimateFreight)
              }}</span>
              /
              {{ goods?.goodsPriceUnitName }}
            </div>
          </n-flex>
        </a>
      </div>
      <a :href="linkUrl" class="more-button flex justify-center items-center">
        <div class="text-[0.36rem] text-[#B25C41]">
          {{ authStore.i18n("cm_app.viewMore") }}
        </div>
        <icon-card size="22" name="icon-park-outline:right" color="#B25C41" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const props = defineProps({
  cateInfo: {
    type: Object,
    default: () => {},
  },
  goodsList: {
    type: Array,
    default: () => [],
  },
  cateColor: {
    type: Object,
    default: () => {
      return {
        textColor: "#272727",
        cateColor: "#f6f6f6",
      };
    },
  },
  tag: {
    type: String,
    default: "",
  },
});
const linkUrl = computed(() => {
  const cateInfo = props.cateInfo;
  if (!cateInfo) return "/h5/search/list";

  const params = new URLSearchParams();

  if (cateInfo.activityId) {
    if (cateInfo.selectorType === "GOODS_SELECTOR_TYPE_MARKETING_RULE") {
      params.append("marketingRuleId", cateInfo.selectorId);
    } else {
      params.append("tagId", cateInfo.selectorId);
    }
    params.append("activityId", cateInfo.activityId);
  } else if (cateInfo.categoryId) {
    params.append("categoryId", cateInfo.categoryId);
    props.cateInfo?.categoryName &&
      params.append("cateName", props.cateInfo?.categoryName);
  } else if (cateInfo.tagId) {
    params.append("tagId", cateInfo.tagId);
  }
  if (cateInfo.padc) {
    params.append("padc", cateInfo.padc);
  }

  if (cateInfo.promotionCode) {
    params.append("promotionCode", cateInfo.promotionCode);
  }

  const queryString = params.toString();
  return `/h5/search/list${queryString ? `?${queryString}` : ""}`;
});
</script>

<style scoped lang="scss">
.category-wrapper {
  height: 2.19rem;
  padding: 0.39rem;
  background: #4671f3;
  margin-bottom: -0.96rem;
  color: #fff;
  .category-content {
    display: flex;
    justify-content: space-between;
  }
  .category-title {
    font-size: 0.34rem;
    font-weight: 400;
    color: #fff;
    width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.goods-wrapper {
  display: flex;
  justify-content: space-between;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  margin-bottom: 0.6rem;
  padding: 0 0.2rem;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .goods-item {
    flex: 0 0 2.75rem;
    width: 2.75rem;
    margin-right: 0.2rem;
    padding: 0.2rem;
    background: #fff;
    border: 0.02rem solid #e8e8e8;
    border-radius: 0.01rem;
    height: fit-content;
    .goods-img {
      position: relative;
      width: 3.03rem;
      height: 2.31rem;
      margin-bottom: 0.2rem;
    }
    .goods-title {
      width: 100%;
      font-size: 0.26rem;
      font-weight: 400;
      color: #212121;
      line-height: 0.32rem;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 0.1rem;
    }
    .goods-price {
      font-size: 0.26rem;
      font-weight: 600;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
}
.more-button {
  flex: 0 0 2.75rem;
  width: 2.75rem;
  height: 4.08rem;
  margin-right: 0.2rem;
  padding: 0.2rem;
  background: #fff;
  border: 0.04rem solid #f2d6cc;
  border-radius: 0.04rem;
}

:deep(.n-image img) {
  width: 100%;
  height: 100%;
}
</style>
