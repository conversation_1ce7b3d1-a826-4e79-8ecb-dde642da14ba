<template>
  <div class="mt-[0.24rem] mb-[0.08rem] w-full text-[0.28rem]">
    <div class="flex">
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        class="w-[1.36rem] h-[1.36rem] shrink-0 mr-[0.24rem] cursor-pointer"
        :src="goods.mainImageUrl"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
      <div class="flex-1 text-[0.28rem] leading-[0.4rem]">
        <n-ellipsis
          :line-clamp="2"
          class="w-full text-[0.28rem] leading-[0.38rem]"
          :tooltip="false"
          :class="!from ? 'hover:underline cursor-pointer' : ''"
        >
          {{ goods.goodsName }}
        </n-ellipsis>
        <div class="flex justify-between">
          <div>
            <span class="text-gray-500 break-all">
              <span class="mr-[0.12rem]">Código: {{ goods.goodsNo }}</span>
              <template v-if="goods.paName">
                <a
                  :href="`/h5/search/list?padc=${goods.padc}`"
                  class="inline-flex items-center max-w-full"
                >
                  <span
                    class="flex-shrink-0 h-[0.34rem] text-[0.24rem] leading-[0.34rem] border border-[#FF4056] rounded-tl-[0.04rem] rounded-bl-[0.04rem] border-r-0 text-[#FF4056] px-[0.08rem] whitespace-nowrap overflow-hidden text-ellipsis"
                  >
                    {{ goods.paName }}
                  </span>
                  <img
                    src="@/assets/icons/common/tag.svg"
                    :alt="goods.paName"
                    class="h-[0.34rem] flex-shrink-0"
                    referrerpolicy="no-referrer"
                  />
                </a>
              </template>
            </span>

            <div
              v-if="props.from === 'submit' && goods.selectedGoodsQty"
              class="text-gray-500"
            >
              {{ authStore.i18n("cm_goods.orderQuantity") }}
              {{ goods.selectedGoodsQty }}
              {{ goods.goodsPriceUnitName }}
            </div>
            <div class="text-gray-500 shrink-0" v-else>
              {{ authStore.i18n("cm_goods.minOrder") }}:
              {{ goods.minBuyQuantity }}
              {{ goods.goodsPriceUnitName }}
            </div>
          </div>
          <slot></slot>
        </div>
        <!-- 预估运费 -->
        <div class="flex flex-wrap text-gray-500">
          <div v-if="goods.supportOnlineOrder">
            <div v-if="goods.estimateFreight">
              {{ authStore.i18n("cm_common.finalShippingCost") }}:
              <span>{{ setUnit(goods.estimateFreight) }}</span>
              <span class="ml-[0.08rem]">
                {{ authStore.i18n("cm_goods.perUnit") }}
                {{ goods.selectedGoodsQty }}
                {{ goods?.goodsPriceUnitName }}
              </span>
            </div>
          </div>
          <div class="flex flex-wrap gap-[0.08rem]" v-else>
            {{ authStore.i18n("cm_goods.shippingCost") }}:
            <div v-if="goods.estimateFreight">
              <span>{{ setUnit(goods.estimateFreight) }}</span>
              <span class="ml-[0.08rem]">
                {{ authStore.i18n("cm_goods.perUnit") }}
                {{ goods.selectedGoodsQty }}
                {{ goods?.goodsPriceUnitName }}
              </span>
            </div>
            <n-popover trigger="hover" raw v-else>
              <template #trigger>
                <div class="cursor-pointer">
                  <span>{{
                    authStore.i18n("cm_goods.pendingConfirmation")
                  }}</span>
                  <icon-card
                    size="16"
                    color="#F7BA2A"
                    class="ml-[0.04rem]"
                    name="mingcute:warning-line"
                  ></icon-card>
                </div>
              </template>
              <div
                style="
                  width: 180px;
                  padding: 6px 14px;
                  background-color: #fff4d4;
                  transform-origin: inherit;
                  border: 1px solid #f7ba2a;
                "
              >
                {{ authStore.i18n("cm_goods.freightConfirmation") }}
              </div>
            </n-popover>
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        v-if="
          goods.skuSelected && goods.skuSelectedQuantity < goods.minBuyQuantity
        "
        class="bg-[#FFF1F1] px-[0.16rem] py-[0.08rem] flex items-center"
      >
        <icon-card
          size="20"
          name="mingcute:warning-line"
          color="#E52828"
          class="mr-[0.16rem]"
        ></icon-card>
        {{ authStore.i18n("cm_find.minBuyQuantity") }}
        {{ goods.minBuyQuantity }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";

const router = useRouter();
const authStore = useAuthStore();
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  from: {
    type: String,
    default: "",
  },
});
</script>
